import '../storage_service.dart';
import 'category_finder_service.dart';
import 'entity_extractor_base.dart';
import 'transaction_parsing_service.dart';
import '../../models/parse_result.dart';

/// Deprecated façade for MlKitParserService that delegates to TransactionParsingService
@Deprecated('Use TransactionParsingService instead. This class will be removed in a future version.')
class MlKitParserService {
  static MlKitParserService? _instance;
  TransactionParsingService? _transactionParsingService;

  MlKitParserService._();

  /// Factory constructor to get singleton instance
  /// [entityExtractor] - Optional entity extractor for dependency injection (mainly for testing)
  static Future<MlKitParserService> getInstance(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._transactionParsingService = await TransactionParsingService.getInstance(
        storageService,
        entityExtractor: entityExtractor,
      );
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    TransactionParsingService.resetInstance();
    _instance = null;
  }

  /// Static background initialization method for deferred loading
  static Future<MlKitParserService> initializeInBackground(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
  }) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._transactionParsingService = await TransactionParsingService.initializeInBackground(
        storageService,
        entityExtractor: entityExtractor,
      );
    }
    return _instance!;
  }

  /// Check if ML Kit is fully initialized and ready
  bool get isReady => _transactionParsingService?.isReady ?? false;

  /// Get the category finder service for advanced category suggestions
  CategoryFinderService get categoryFinder => _transactionParsingService!.categoryFinder;

  /// Main entry point for parsing transactions
  Future<ParseResult> parseTransaction(String text) async {
    return await _transactionParsingService!.parseTransaction(text);
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    return await _transactionParsingService!.learnCategory(text, categoryId);
  }

  /// Complete transaction parsing with user-confirmed amount
  Future<ParseResult> completeTransaction(String originalText, double confirmedAmount) async {
    return await _transactionParsingService!.completeTransaction(originalText, confirmedAmount);
  }

  /// Dispose resources
  void dispose() {
    _transactionParsingService?.dispose();
  }
}