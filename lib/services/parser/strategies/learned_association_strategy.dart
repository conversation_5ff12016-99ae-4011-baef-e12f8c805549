import 'dart:async';
import 'package:uuid/uuid.dart';
import '../../../models/transaction_model.dart';
import '../../../models/parse_result.dart';
import '../../../utils/amount_utils.dart';
import '../../../utils/currency_utils.dart';
import '../learned_association_service.dart';
import '../../storage_service.dart';
import '../parsing_strategy.dart';
import '../parsing_context.dart';

/// Strategy that handles learned association checking.
/// 
/// This strategy checks if the input text matches any previously learned
/// associations and builds a transaction from the association data if found.
/// Returns null if no association is found, allowing other strategies to handle
/// the input.
class LearnedAssociationStrategy implements ParsingStrategy {
  final LearnedAssociationService _learnedAssociationService;
  final StorageService _storageService;
  final Uuid _uuid;

  /// Creates a new LearnedAssociationStrategy with required dependencies.
  /// 
  /// [learnedAssociationService] - Service for retrieving learned associations
  /// [storageService] - Service for accessing storage (used for default currency)
  /// [uuid] - UUID generator for creating transaction IDs
  LearnedAssociationStrategy(
    this._learnedAssociationService,
    this._storageService,
    this._uuid,
  );

  @override
  String get name => 'LearnedAssociationStrategy';

  @override
  Future<ParseResult?> execute(ParsingContext context) async {
    try {
      // Check for learned associations
      final learnedAssociation = await _learnedAssociationService.getAssociation(context.text);
      
      if (learnedAssociation == null) {
        // No association found, decline to handle this input
        return null;
      }

      // Build transaction from association
      final transaction = await _buildTransactionFromAssociation(context.text, learnedAssociation);
      return ParseResult.success(transaction);
      
    } catch (e) {
      // Log error but don't throw to avoid breaking the parsing flow
      print('Error in LearnedAssociationStrategy: $e');
      return null; // Decline to handle on error
    }
  }

  /// Build a transaction from a learned association
  /// 
  /// This method is copied from MlKitParserService._buildTransactionFromAssociation
  /// to maintain the same logic for building transactions from learned associations.
  Future<Transaction> _buildTransactionFromAssociation(String text, LearnedAssociation association) async {
    // Use confirmed amount from association if available, otherwise extract from text
    double amount = 0.0;

    // Get default currency from storage instead of hardcoding USD
    final defaultCurrency = await _storageService.getDefaultCurrency();

    // Prioritize confirmed amount from learned association
    if (association.confirmedAmount != null) {
      amount = association.confirmedAmount!;
    } else {
      // Enhanced amount extraction for learned associations with abbreviation support
      final amountResult = AmountUtils.extractAmountFromText(text);
      if (amountResult != null) {
        amount = amountResult['amount'] as double;
      }
    }

    // Extract currency from text, fallback to default currency
    String currencyCode = _extractCurrencyFromText(text) ?? defaultCurrency;

    return Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: association.type ?? TransactionType.expense,
      categoryId: association.categoryId ?? 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: currencyCode,
    );
  }

  /// Extract currency from text
  /// 
  /// This method is copied from MlKitParserService._extractCurrencyFromText
  /// to maintain consistent currency detection logic.
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Extract hashtags from text
  /// 
  /// This method is copied from MlKitParserService._extractTags
  /// to maintain consistent tag extraction logic.
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }
}
